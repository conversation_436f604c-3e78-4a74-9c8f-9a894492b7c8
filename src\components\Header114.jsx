"use client";

import { motion } from "framer-motion";
import React, { useEffect, useRef, useState, useCallback } from "react";
import { Button1 } from "./Button1";


export function Header114({
  title = "Walk the talk, zeggen ze.<br> Wij doen het.",
  subtitle = "Samen maken we<br> duur<PERSON><PERSON>heid tastbaar.",
  description = "Klinkt mooi, maar we dóen het ook. <PERSON><PERSON> met jullie, bedrijven groot en klein. En dan hebben we het over duurzaamheid in al z'n vormen. Over impact maken. Op alle mogelijke manieren. Dat is de meerwaarde van ons ecosysteem. We rollen de mouwen op om samen lokaal natuur te creëren en jouw ESG-verhaal tastbaar te maken. Om jouw bedrijf een stem te geven, zodat je anderen kan inspireren. En om de betrokkenheid van jouw stakeholders te vergroten door ze slim én leuk te verbinden. Zorgen voor daadkracht. En draagvlak creëren. Inspireren en verbinden. Dat is wat we doen!",
  backgroundImage = "/images/forestforward/homepage/9.png",
  backgroundVideo = null,
  primaryButtonText = "Ontdek meer",
  primaryButtonAction = null,
  secondaryButtonText = "Contacteer ons",
  secondaryButtonHref = "/contact",
  tertiaryButtonText = "Contacteer ons",
  tertiaryButtonHref = "/contact",
  showButtons = true
}) {
  const sectionRef = useRef(null);
  const [isFixed, setIsFixed] = useState(true);
  const [backgroundTop, setBackgroundTop] = useState(0);
  const [parallaxOffset, setParallaxOffset] = useState(0);
  const [finalParallaxOffset, setFinalParallaxOffset] = useState(0);
  const [isMobile, setIsMobile] = useState(false);

  const scrollToNextSection = () => {
    const nextSection = document.getElementById('second-section');
    if (nextSection) {
      nextSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Throttled scroll handler for better performance
  const handleScroll = useCallback(() => {
    if (!sectionRef.current) return;

    const sectionRect = sectionRef.current.getBoundingClientRect();
    const sectionBottom = sectionRect.bottom;
    const viewportHeight = window.innerHeight;
    const scrollY = window.scrollY;

    // Calculate parallax offset - reduce effect on mobile for better performance
    const sectionTop = sectionRect.top + scrollY;
    const scrollProgress = Math.max(0, scrollY - sectionTop);
    const parallaxSpeed = isMobile ? 0.05 : 0.15; // Further reduced speed to minimize cropping
    const currentParallaxOffset = scrollProgress * parallaxSpeed;

    // Use a smaller buffer and handle transitions more smoothly
    const transitionBuffer = 2;

    // When the section bottom reaches the bottom of the viewport, disable fixed background
    if (sectionBottom <= viewportHeight + transitionBuffer) {
      if (isFixed) {
        // Store the current parallax offset when transitioning from fixed to absolute
        setFinalParallaxOffset(currentParallaxOffset);
      }
      setIsFixed(false);
      // Calculate where the background should be positioned to maintain continuity
      const sectionHeight = sectionRef.current.offsetHeight;
      setBackgroundTop(sectionTop + sectionHeight - viewportHeight);
    } else if (sectionBottom > viewportHeight + transitionBuffer) {
      // Only transition back to fixed if we're clearly above the threshold
      if (!isFixed) {
        // When transitioning back to fixed, reset the parallax offset smoothly
        setParallaxOffset(currentParallaxOffset);
      }
      setIsFixed(true);
      setBackgroundTop(0);
    }

    // Always update parallax offset when in fixed mode
    if (isFixed || sectionBottom > viewportHeight + transitionBuffer) {
      setParallaxOffset(currentParallaxOffset);
    }
  }, [isFixed, isMobile]);

  useEffect(() => {
    // Throttle scroll events for better performance
    let ticking = false;

    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    // Initial call
    handleScroll();

    window.addEventListener('scroll', throttledHandleScroll, { passive: true });
    return () => window.removeEventListener('scroll', throttledHandleScroll);
  }, [handleScroll]);

  // Determine background style based on whether it's an image or video
  const backgroundStyle = backgroundVideo ? {} : {
    backgroundImage: `url('${backgroundImage}')`,
    backgroundSize: 'cover',
    backgroundPosition: `center ${50 + (isFixed ? parallaxOffset/20 : finalParallaxOffset/20)}%`,
    backgroundRepeat: "no-repeat",
    backgroundAttachment: 'scroll',
  };

  return (
    <section ref={sectionRef} id="relume" className="relative min-h-[200vh]">
      {/* Sticky background image/video that stays fixed while content scrolls over it */}
      <div
        className={`${isFixed ? 'fixed' : 'absolute'} w-full -z-50`}
        style={{
          height: '110vh', // Further reduced height to minimize cropping
          top: isFixed ? '-5vh' : `${backgroundTop - (window.innerHeight * 0.05)}px`,
          left: 0,
          right: 0,
          ...backgroundStyle,
          // Minimal parallax effect to minimize cropping
          transform: !isMobile && isFixed ? `translateY(${-parallaxOffset * 0.2}px)` :
                     !isMobile && !isFixed ? `translateY(${-finalParallaxOffset * 0.2}px)` : 'none',
          // Smooth transition only when switching between fixed and absolute
          transition: 'none',
          willChange: 'transform, background-position' // Optimize for animations
        }}
      >
        {/* Background video if provided */}
        {backgroundVideo && (
          <video
            autoPlay
            muted
            loop
            playsInline
            className="absolute inset-0 w-full h-full object-cover"
            style={{
              transform: !isMobile && isFixed ? `translateY(${-parallaxOffset * 0.2}px)` :
                         !isMobile && !isFixed ? `translateY(${-finalParallaxOffset * 0.2}px)` : 'none',
            }}
          >
            <source src={backgroundVideo} type="video/mp4" />
          </video>
        )}
        <div className="absolute inset-0 bg-black/75" />
      </div>

      {/* Content that scrolls over the sticky background */}
      <div className="absolute inset-0 z-50 px-[5%]">
        <div className="container min-h-[200vh] flex flex-col justify-between">
          {/* First content section */}
          <div className="flex flex-col justify-center min-h-screen py-20">
            <div className="w-full max-w-4xl pl-[5%] pr-[20%]">
              <motion.h1
                className="text-6xl font-semibold text-text-alternative md:text-9xl lg:text-10xl mb-6 md:mb-8"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                dangerouslySetInnerHTML={{ __html: title }}
              />
              {showButtons && (
                <motion.div
                  className="mt-6 flex flex-wrap gap-4 md:mt-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  <Button1
                    title="Button"
                    variant="filled"
                    onClick={primaryButtonAction || scrollToNextSection}
                  >
                    {primaryButtonText}
                  </Button1>
                  <Button1
                    title="Button"
                    variant="transparent"
                    href={secondaryButtonHref}
                  >
                    {secondaryButtonText}
                  </Button1>
                </motion.div>
              )}
            </div>
          </div>

          {/* Second content section */}
          <motion.div
            id="second-section"
            className="flex flex-col justify-center min-h-screen py-20"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true, amount: 0.3 }}
          >
            <div className="w-full max-w-2xl pl-[5%] md:pl-[40%] pr-[5%]">
              <motion.h1
                className="text-4xl font-semibold text-text-alternative md:text-5xl lg:text-6xl mb-6"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                dangerouslySetInnerHTML={{ __html: subtitle }}
              />
              <motion.p
                className="text-text-alternative md:text-md"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                viewport={{ once: true }}
                dangerouslySetInnerHTML={{ __html: description }}
              >
              </motion.p>
              <motion.div
                  className="mt-6 flex flex-wrap gap-4 md:mt-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  <Button1
                    title={tertiaryButtonText}
                    href={tertiaryButtonHref}
                    variant="transparent"
                  >
                    {tertiaryButtonText}
                  </Button1>
                </motion.div>
              
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
