import React from "react";
import { Navbar2 } from "./_components/Navbar";
import { Layout394 } from "../components/EcoLayout";
import { Logo3 } from "../components/Logo3";
import { Blog36 } from "./_components/home/<USER>";
import { Gallery1 } from "../components/Gallery1";
import { Footer2 } from "./_components/Footer";
import { Header114 } from "../components/Header114";

export default function Page() {
  // Define images for the homepage gallery
  const galleryImages = [
    {
      src: "/images/algemeen/moodboard 1.png",
      alt: "Moodboard image 1"
    },
    {
      src: "/images/algemeen/moodboard 2.png",
      alt: "Moodboard image 2"
    },
    {
      src: "/images/algemeen/moodboard 3.png",
      alt: "Moodboard image 3"
    },
    {
      src: "/images/algemeen/moodboard 4.png",
      alt: "Moodboard image 4"
    },
    {
      src: "/images/algemeen/moodboard 5.png",
      alt: "Moodboard image 5"
    },
    {
      src: "/images/algemeen/moodboard 6.png",
      alt: "Moodboard image 6"
    },
    {
      src: "/images/algemeen/moodboard 7.png",
      alt: "Moodboard image 7"
    }
  ];

  return (
    <div className="theme-homepage">
      <Navbar2 />
      <Header114
        title = "Walk the talk, zeggen ze.<br> Wij doen het."
        subtitle = "Samen maken we<br> duurzaamheid tastbaar."
        description = "Klinkt mooi, maar we dóen het ook. Samen met jullie, bedrijven groot en klein. En dan hebben we het over duurzaamheid in al z'n vormen. Over impact maken. Op alle mogelijke manieren. Dat is de meerwaarde van ons ecosysteem. We rollen de mouwen op om samen lokaal natuur te creëren en jouw ESG-verhaal tastbaar te maken. Om jouw bedrijf een stem te geven, zodat je anderen kan inspireren. En om de betrokkenheid van jouw stakeholders te vergroten door ze slim én leuk te verbinden. Zorgen voor daadkracht. En draagvlak creëren. Inspireren en verbinden. Dat is wat we doen!"
        backgroundImage = "/images/forestforward/homepage/9.png"
        primaryButtonText = "Ontdek meer"
        secondaryButtonText = "Contacteer ons"
        secondaryButtonHref = "/contact"
        tertiaryButtonText = "Contacteer ons"
        tertiaryButtonHref = "/contact"
        />
      {/* <Header114 /> */}
      <Layout394 />
      <Logo3 />
      <Blog36 />
      <Gallery1
        images={galleryImages}
        title="Moodboard"
      />
      <Footer2 />
    </div>
  );
}
